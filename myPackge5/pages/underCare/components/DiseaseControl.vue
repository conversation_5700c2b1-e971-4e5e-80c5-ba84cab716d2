<template>
  <view class="under-care-container">
    <scroll-view
      class="under-care-list"
      scroll-y
      :scroll-with-animation="true"
      @scrolltolower="scrollToLower"
      refresher-enabled
      :refresher-triggered="refresherState"
      @refresherrefresh="bindrefresherrefresh"
    >
      <view v-for="(item, index) in list" :key="index" class="list-item">
        <view class="item-header">
          <text class="item-time">{{ item.operateTime }}</text>
        </view>
        <view class="item-content">
          <view class="content-row">
            <text class="content-label">接种对象耳标：</text>
            <text class="content-value">{{ item.earTagNo }}</text>
          </view>
          <view class="content-row">
            <text class="content-label">疫苗名称：</text>
            <text class="content-value dict-value">{{ item.vaccineName }}</text>
          </view>
          <view class="content-row">
            <text class="content-label">接种剂量：</text>
            <text class="content-value">{{ item.vaccineDose }}</text>
          </view>
          <view class="content-row">
            <text class="content-label">疫苗厂家：</text>
            <text class="content-value">{{ item.company }}</text>
          </view>
          <view class="content-row" v-if="item.operatePeopleName">
            <text class="content-label">操作人：</text>
            <text class="content-value">{{ item.operatePeopleName }}</text>
          </view>
          <view class="content-row" v-if="item.operateTypeName">
            <text class="content-label">接种方式：</text>
            <text class="content-value">{{ item.operateTypeName }}</text>
          </view>
          <view class="content-row" v-if="item.remark">
            <text class="content-label">备注：</text>
            <text class="content-value remark-text">{{ item.remark }}</text>
          </view>
        </view>
      </view>

      <nullList v-if="isEmpty" />
      <view v-if="!noMore && list.length > 0" class="load-more">加载更多...</view>
      <view v-if="noMore && list.length > 0" class="load-more">没有更多数据了</view>
    </scroll-view>

    <!-- 固定新增按钮 -->
    <view class="fixed-add-btn" @click="addRecord">
      <image class="add-icon" src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/xiaoshouhetong/add.png" />
    </view>
  </view>
</template>

<script>
import { diseaPage } from '@/api/pages/livestock/underCare'
import nullList from '@/components/null-list/index.vue'

export default {
  name: 'DiseaseControl',
  components: {
    nullList
  },
  data() {
    return {
      refresherState: false,
      noMore: false,
      isEmpty: false,
      list: [],
      pageNum: 1,
      pageSize: 10
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      uni.showLoading({
        title: '加载中',
        icon: 'none'
      })

      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        earTagNo: '',
        startTime: '',
        endTime: ''
      }

      diseaPage(params).then(response => {
        if (response.code === 200) {
          const newList = response.result?.list || []
          const total = response.result?.total || 0

          if (this.pageNum >= 2) {
            this.list = this.list.concat(newList)
            this.list.length >= total ? this.noMore = true : this.noMore = false
          } else {
            if (total >= 1) {
              this.isEmpty = false
              this.list = newList
              this.list.length >= total ? this.noMore = true : this.noMore = false
            } else {
              this.isEmpty = true
            }
          }
        }
      }).catch(error => {
        console.error('获取疾病防控列表失败:', error)
        this.$toast('获取数据失败')
      }).finally(() => {
        uni.hideLoading()
      })
    },

    scrollToLower() {
      if (this.noMore) return
      this.pageNum++
      this.getList()
    },

    bindrefresherrefresh() {
      this.refresherState = true
      this.pageNum = 1
      this.noMore = false
      this.getList()
      setTimeout(() => {
        this.refresherState = false
        this.$toast('刷新成功')
      }, 1000)
    },

    addRecord() {
      // 新增记录逻辑
      this.$toast('新增疾病防控记录')
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/common/css/underCareList.scss';
</style>
